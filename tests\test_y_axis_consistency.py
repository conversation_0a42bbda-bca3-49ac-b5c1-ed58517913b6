"""
Test script to verify Y-axis value consistency between different plot types.
This script helps identify and debug discrepancies in generation data totals.
"""

import pandas as pd
import datetime
from backend.logs.logger_setup import setup_logger
from backend.data.data import (
    get_generation_consumption_comparison,
    compare_generation_consumption,
    get_tod_binned_data
)

# Setup logger
logger = setup_logger(__name__)

def test_y_axis_consistency(plant_name, test_date):
    """
    Test Y-axis value consistency between ToD plots and Generation vs Consumption plots.
    
    Args:
        plant_name (str): Name of the plant to test
        test_date (datetime): Date to test
    
    Returns:
        dict: Test results with totals from different data sources
    """
    logger.info(f"Testing Y-axis consistency for {plant_name} on {test_date}")
    
    results = {
        'plant_name': plant_name,
        'test_date': test_date,
        'generation_vs_consumption_total': 0,
        'tod_plot_total': 0,
        'discrepancy_kwh': 0,
        'discrepancy_percentage': 0,
        'status': 'UNKNOWN'
    }
    
    try:
        # Test 1: Get Generation vs Consumption data
        logger.info("=== Testing Generation vs Consumption Data ===")
        generation_df, consumption_df = get_generation_consumption_comparison(plant_name, test_date)
        
        if not generation_df.empty and not consumption_df.empty:
            comparison_df = compare_generation_consumption(generation_df, consumption_df)
            if not comparison_df.empty:
                gen_vs_cons_total = comparison_df['generation_kwh'].sum()
                results['generation_vs_consumption_total'] = gen_vs_cons_total
                logger.info(f"Generation vs Consumption total: {gen_vs_cons_total:.2f} kWh")
            else:
                logger.warning("Empty comparison dataframe")
        else:
            logger.warning("Empty generation or consumption dataframes")
        
        # Test 2: Get ToD binned data
        logger.info("=== Testing ToD Plot Data ===")
        tod_df = get_tod_binned_data(plant_name, test_date)
        
        if not tod_df.empty and 'generation_kwh' in tod_df.columns:
            tod_total = tod_df['generation_kwh'].sum()
            results['tod_plot_total'] = tod_total
            logger.info(f"ToD plot total: {tod_total:.2f} kWh")
        else:
            logger.warning("Empty ToD dataframe or missing generation_kwh column")
        
        # Calculate discrepancy
        if results['generation_vs_consumption_total'] > 0 and results['tod_plot_total'] > 0:
            discrepancy_kwh = abs(results['generation_vs_consumption_total'] - results['tod_plot_total'])
            results['discrepancy_kwh'] = discrepancy_kwh
            
            # Calculate percentage discrepancy
            avg_total = (results['generation_vs_consumption_total'] + results['tod_plot_total']) / 2
            discrepancy_percentage = (discrepancy_kwh / avg_total) * 100
            results['discrepancy_percentage'] = discrepancy_percentage
            
            # Determine status
            if discrepancy_percentage < 1.0:  # Less than 1% difference
                results['status'] = 'PASS'
            elif discrepancy_percentage < 5.0:  # Less than 5% difference
                results['status'] = 'WARNING'
            else:
                results['status'] = 'FAIL'
            
            logger.info(f"Discrepancy: {discrepancy_kwh:.2f} kWh ({discrepancy_percentage:.2f}%)")
            logger.info(f"Test Status: {results['status']}")
        else:
            results['status'] = 'NO_DATA'
            logger.warning("Insufficient data to calculate discrepancy")
    
    except Exception as e:
        logger.error(f"Error during Y-axis consistency test: {e}")
        results['status'] = 'ERROR'
        results['error'] = str(e)
    
    return results

def run_comprehensive_test(plant_names, test_dates):
    """
    Run comprehensive Y-axis consistency tests for multiple plants and dates.
    
    Args:
        plant_names (list): List of plant names to test
        test_dates (list): List of dates to test
    
    Returns:
        list: List of test results
    """
    all_results = []
    
    for plant_name in plant_names:
        for test_date in test_dates:
            result = test_y_axis_consistency(plant_name, test_date)
            all_results.append(result)
    
    # Summary
    logger.info("=== TEST SUMMARY ===")
    pass_count = sum(1 for r in all_results if r['status'] == 'PASS')
    warning_count = sum(1 for r in all_results if r['status'] == 'WARNING')
    fail_count = sum(1 for r in all_results if r['status'] == 'FAIL')
    error_count = sum(1 for r in all_results if r['status'] == 'ERROR')
    no_data_count = sum(1 for r in all_results if r['status'] == 'NO_DATA')
    
    logger.info(f"Total tests: {len(all_results)}")
    logger.info(f"PASS: {pass_count}")
    logger.info(f"WARNING: {warning_count}")
    logger.info(f"FAIL: {fail_count}")
    logger.info(f"ERROR: {error_count}")
    logger.info(f"NO_DATA: {no_data_count}")
    
    # Show failed tests
    failed_tests = [r for r in all_results if r['status'] == 'FAIL']
    if failed_tests:
        logger.warning("FAILED TESTS:")
        for test in failed_tests:
            logger.warning(f"  {test['plant_name']} on {test['test_date']}: "
                         f"{test['discrepancy_kwh']:.2f} kWh ({test['discrepancy_percentage']:.2f}%)")
    
    return all_results

if __name__ == "__main__":
    # Example usage
    test_plants = ["Kids Clinic India Limited"]  # Add your plant names here
    test_dates = [
        datetime.datetime(2024, 1, 15),
        datetime.datetime(2024, 1, 16),
        datetime.datetime(2024, 1, 17)
    ]
    
    results = run_comprehensive_test(test_plants, test_dates)
    
    # Save results to CSV for analysis
    if results:
        df = pd.DataFrame(results)
        df.to_csv('y_axis_consistency_test_results.csv', index=False)
        logger.info("Test results saved to y_axis_consistency_test_results.csv")
